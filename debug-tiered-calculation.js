// Debug script to test tiered calculation logic
// This mimics the logic from charge-processor.service.ts

function calculateTiered(meterValue, tiers) {
  console.log(`\n=== Calculating tiered pricing ===`);
  console.log(`Meter value: ${meterValue}`);
  console.log(`Tiers:`, JSON.stringify(tiers, null, 2));

  let totalAmount = 0;
  let remainingUnits = meterValue;
  const tieredBreakdown = [];

  for (let i = 0; i < tiers.length && remainingUnits > 0; i++) {
    const tier = tiers[i];
    const tierMax = tier.To === 'inf' ? Infinity : tier.To;
    const unitsInTier = Math.min(remainingUnits, tierMax - tier.From);

    console.log(`\nTier ${i}:`);
    console.log(`  From: ${tier.From}, To: ${tier.To}`);
    console.log(`  Remaining units: ${remainingUnits}`);
    console.log(`  Units in this tier: ${unitsInTier}`);

    if (unitsInTier <= 0) {
      console.log(`  Skipping tier (no units)`);
      continue;
    }

    let tierAmount = tier.Flat_fee || 0;
    console.log(`  Flat fee: ${tier.Flat_fee || 0}`);
    
    if (tier.Rate) {
      const rateAmount = tier.Rate * unitsInTier;
      tierAmount += rateAmount;
      console.log(`  Rate: ${tier.Rate} * ${unitsInTier} = ${rateAmount}`);
    }

    console.log(`  Tier amount: ${tierAmount}`);
    totalAmount += tierAmount;
    remainingUnits -= unitsInTier;

    tieredBreakdown.push({
      tierIndex: i,
      from: tier.From,
      to: tier.To,
      unitsInTier,
      flatFee: tier.Flat_fee,
      rate: tier.Rate,
      tierAmount,
    });
  }

  console.log(`\n=== Final Result ===`);
  console.log(`Total amount: ${totalAmount}`);
  console.log(`Breakdown:`, tieredBreakdown);
  
  return {
    amount: totalAmount,
    breakdown: tieredBreakdown
  };
}

// Test cases
console.log("=".repeat(50));
console.log("TESTING TIERED CALCULATION");
console.log("=".repeat(50));

// Case 1: Correct configuration (what you should have)
console.log("\n1. CORRECT CONFIGURATION:");
const correctTiers = [
  { From: 0, To: 5, Flat_fee: 50 },
  { From: 5, To: 'inf', Rate: 10 }
];
calculateTiered(15, correctTiers);

// Case 2: Incorrect configuration (might be what you have)
console.log("\n2. INCORRECT CONFIGURATION (both flat and rate in first tier):");
const incorrectTiers = [
  { From: 0, To: 5, Flat_fee: 50, Rate: 10 },
  { From: 5, To: 'inf', Rate: 10 }
];
calculateTiered(15, incorrectTiers);

// Case 3: Another possible incorrect configuration
console.log("\n3. ANOTHER POSSIBLE ISSUE (overlapping ranges):");
const overlappingTiers = [
  { From: 0, To: 5, Flat_fee: 50 },
  { From: 0, To: 'inf', Rate: 10 }
];
calculateTiered(15, overlappingTiers);

// Case 4: Test with exact boundaries
console.log("\n4. BOUNDARY TEST (exactly 5 km):");
calculateTiered(5, correctTiers);

console.log("\n" + "=".repeat(50));
console.log("ANALYSIS:");
console.log("=".repeat(50));
console.log("If you're getting 151 instead of 150, check:");
console.log("1. Your tier configuration - make sure you don't have both Flat_fee and Rate in the same tier");
console.log("2. Check if there are any rounding operations elsewhere");
console.log("3. Check if there are multiple charges being added");
console.log("4. Check if there's a default/minimum fare being added");
console.log("5. Enable debug logging in the fare engine to see the exact breakdown");
