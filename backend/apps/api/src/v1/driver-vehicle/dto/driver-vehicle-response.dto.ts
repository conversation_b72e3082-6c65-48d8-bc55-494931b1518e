import { ApiProperty } from '@nestjs/swagger';
import { DriverVehicleStatus } from '@shared/shared/repositories/models/driverVehicle.model';
import { DriverVehicleDocumentStatus } from '@shared/shared/repositories/models/driverVehicleDocument.model';

export class VehicleTypeResponseDto {
  @ApiProperty({ example: 'vehicle-type-uuid' })
  id!: string;

  @ApiProperty({ example: 'Sedan' })
  name!: string;

  @ApiProperty({ example: 'Four-door passenger car', required: false })
  description?: string;

  @ApiProperty({ example: 'https://example.com/sedan.png', required: false })
  image?: string;
}

export class CityProductResponseDto {
  @ApiProperty({ example: 'city-product-uuid' })
  id!: string;

  @ApiProperty({ example: 'city-uuid' })
  cityId!: string;

  @ApiProperty({ example: 'product-uuid' })
  productId!: string;

  @ApiProperty({ example: true })
  isEnabled!: boolean;
}

export class UserResponseDto {
  @ApiProperty({ example: 'user-uuid' })
  id!: string;

  @ApiProperty({ example: '+************' })
  phoneNumber!: string;

  @ApiProperty({ example: '<EMAIL>', required: false })
  email?: string;
}

export class UserProfileResponseDto {
  @ApiProperty({ example: 'profile-uuid' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;

  @ApiProperty({ type: UserResponseDto })
  user!: UserResponseDto;
}

export class RoleResponseDto {
  @ApiProperty({ example: 'role-uuid' })
  id!: string;

  @ApiProperty({ example: 'driver' })
  name!: string;
}

export class CreatedByUserResponseDto {
  @ApiProperty({ example: 'profile-uuid' })
  id!: string;

  @ApiProperty({ example: 'John', required: false, nullable: true })
  firstName?: string | null;

  @ApiProperty({ example: 'Doe', required: false, nullable: true })
  lastName?: string | null;

  @ApiProperty({ type: RoleResponseDto, required: false })
  role?: RoleResponseDto;
}

export class VehicleDocumentDto {
  @ApiProperty({ example: 'vehicle-document-uuid' })
  id!: string;

  @ApiProperty({ example: 'Registration Certificate' })
  name!: string;

  @ApiProperty({ example: 'RC' })
  identifier!: string;

  @ApiProperty({
    enum: DriverVehicleDocumentStatus,
    example: DriverVehicleDocumentStatus.PENDING,
    required: false,
    description: 'Upload status of the document. Null if not uploaded.',
  })
  uploadStatus?: DriverVehicleDocumentStatus | null;
}

export class DriverVehicleResponseDto {
  @ApiProperty({ example: 'driver-vehicle-uuid' })
  id!: string;

  @ApiProperty({ example: 'profile-uuid' })
  userProfileId!: string;

  @ApiProperty({ example: 'city-product-uuid', required: false })
  cityProductId?: string;

  @ApiProperty({ example: 'vehicle-type-uuid' })
  vehicleTypeId!: string;

  @ApiProperty({ example: 'MH12AB1234' })
  vehicleNumber!: string;

  @ApiProperty({ example: false })
  isNocRequired!: boolean;

  @ApiProperty({ example: false })
  isPrimary!: boolean;

  @ApiProperty({
    enum: DriverVehicleStatus,
    example: DriverVehicleStatus.pending,
  })
  status!: DriverVehicleStatus;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  createdAt!: string;

  @ApiProperty({ example: '2023-12-01T10:00:00Z' })
  updatedAt!: string;

  @ApiProperty({ example: null, required: false })
  deletedAt?: string;

  @ApiProperty({ type: VehicleTypeResponseDto, required: false })
  vehicleType?: VehicleTypeResponseDto;

  @ApiProperty({ type: CityProductResponseDto, required: false })
  cityProduct?: CityProductResponseDto;

  @ApiProperty({ type: UserProfileResponseDto, required: false })
  userProfile?: UserProfileResponseDto;

  @ApiProperty({
    type: [VehicleDocumentDto],
    required: false,
    description: 'List of vehicle documents with their upload status',
  })
  vehicleDocuments?: VehicleDocumentDto[];

  @ApiProperty({
    type: CreatedByUserResponseDto,
    required: false,
    nullable: true,
    description: 'Information about the user who created this driver vehicle',
  })
  createdByUser?: CreatedByUserResponseDto | null;
}
