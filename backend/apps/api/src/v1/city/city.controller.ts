import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  Headers,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
} from '@nestjs/common';
import { Ability } from '@shared/shared/casl/decorators/ability.decorator';
import { CaslAbilityGuard } from '../../common/guards/casl-ability.guard';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiHeader,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import { ChangeCityStatusDto } from './dto/change-city-status.dto';
import { CityGeoListApiResponseDto } from './dto/city-geo-response.dto';
import {
  CityCreateApiResponseDto,
  CityListApiResponseDto,
  CityApiResponseDto,
  CityUpdateApiResponseDto,
  CityDeleteApiResponseDto,
  CityStatusChangeApiResponseDto,
  CityPaginatedApiResponseDto,
} from './dto/city-api-response.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { CityService } from '@shared/shared/modules/city/city.service';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';
import { VehicleTypeService } from '@shared/shared/modules/vehicle-type/vehicle-type.service';
import {
  VehicleTypeListResponseDto,
  TranslatedVehicleTypeExampleDto,
} from '../vehicle-type/dto/vehicle-type-response.dto';

@ApiTags('Cities')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('cities')
export class CityController {
  constructor(
    private readonly cityService: CityService,
    private readonly vehicleTypeService: VehicleTypeService, // Inject VehicleService to fetch vehicles by city ID
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new city' })
  @ApiResponse({ status: 201, type: CityCreateApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @UseGuards(CaslAbilityGuard)
  @Ability('city:create')
  async create(@Body() body: CreateCityDto) {
    const data = await this.cityService.createCity(body as any);
    return {
      success: true,
      message: 'City created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all active cities' })
  @ApiResponse({ status: 200, type: CityListApiResponseDto })
  async findAll() {
    const data = await this.cityService.findAllActiveCities();
    return {
      success: true,
      message: 'Cities fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('admin')
  @ApiOperation({ summary: 'Get all active cities' })
  @ApiResponse({ status: 200, type: CityListApiResponseDto })
  async findAllCitiesFiltered(@Req() request: any) {
    const apiConsumer: ApiConsumer = request.user;
    const data = await this.cityService.findAllActiveCitiesForUser(apiConsumer);
    return {
      success: true,
      message: 'Cities fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('nearby')
  @ApiOperation({
    summary: 'Get cities by geo coordinates',
    description:
      'Find cities within a specified radius of given coordinates using H3 indexing. If geo parameters are not provided or invalid, returns all cities.',
  })
  @ApiQuery({
    name: 'lat',
    required: false,
    type: Number,
    description: 'Latitude coordinate (optional)',
    example: 12.9716,
  })
  @ApiQuery({
    name: 'lng',
    required: false,
    type: Number,
    description: 'Longitude coordinate (optional)',
    example: 77.5946,
  })
  @ApiQuery({
    name: 'radiusInMeter',
    required: false,
    type: Number,
    description: 'Search radius in meters (optional)',
    example: 5000,
  })
  @ApiResponse({ status: 200, type: CityGeoListApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async findCitiesByGeoCoordinates(
    @Query('lat') lat?: number,
    @Query('lng') lng?: number,
    @Query('radiusInMeter') radiusInMeter?: number,
  ) {
    // Check if all geo parameters are provided and valid
    const hasValidGeoParams =
      lat !== undefined &&
      lng !== undefined &&
      radiusInMeter !== undefined &&
      !isNaN(Number(lat)) &&
      !isNaN(Number(lng)) &&
      !isNaN(Number(radiusInMeter));

    let data;
    if (hasValidGeoParams) {
      // Apply geo-filtering
      data = await (this.cityService as any).findCitiesByGeoCoordinates(
        Number(lat),
        Number(lng),
        Number(radiusInMeter),
      );
    } else {
      // Return all cities without geo-filtering and without full URL for icons
      data = await this.cityService.findAllCities();
    }

    return {
      success: true,
      message: hasValidGeoParams
        ? 'Cities fetched successfully by geo coordinates'
        : 'All cities fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginate')
  @ApiOperation({ summary: 'Get paginated cities' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by city name',
  })
  @ApiQuery({
    name: 'state',
    required: false,
    type: String,
    description: 'Filter by state',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['active', 'inactive'],
    description: 'Filter by status',
  })
  @ApiResponse({ status: 200, type: CityPaginatedApiResponseDto })
  async paginate(
    @Query() query: PaginationDto & { state?: string; status?: string },
  ) {
    const result = await this.cityService.paginateCities(
      query.page,
      query.limit,
      query,
    );
    return {
      success: true,
      message: 'Cities paginated successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Get('admin/paginate')
  @ApiOperation({ summary: 'Get paginated cities for admin' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @UseGuards(CaslAbilityGuard)
  @Ability('city:list')
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by city name',
  })
  @ApiQuery({
    name: 'state',
    required: false,
    type: String,
    description: 'Filter by state',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['active', 'inactive'],
    description: 'Filter by status',
  })
  @ApiResponse({ status: 200, type: CityPaginatedApiResponseDto })
  async paginateForAdmin(
    @Query() query: PaginationDto & { state?: string; status?: string },
    @Req() request: any,
  ) {
    const apiConsumer: ApiConsumer = request.user;
    const result = await this.cityService.paginateCitiesForAdmin(
      query.page,
      query.limit,
      query,
      apiConsumer,
    );
    return {
      success: true,
      message: 'Cities paginated successfully for admin',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a city by ID' })
  @ApiResponse({ status: 200, type: CityApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async findOne(@Param('id') id: string) {
    const data = await this.cityService.findCityById(id);
    return {
      success: true,
      message: 'City fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':cityId/vehicles')
  @ApiOperation({
    summary: 'Get vehicles by city ID',
    description:
      'Get active vehicle types for a city. Supports language translation via Language-Code header.',
  })
  @ApiHeader({
    name: 'Language-Code',
    description: 'Language code for translation (e.g., en, fr, ml)',
    required: false,
    example: 'ml',
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicles fetched successfully (without Language-Code header)',
    type: ApiResponseDto<VehicleTypeListResponseDto>,
  })
  @ApiResponse({
    status: 200,
    description:
      'Vehicles fetched successfully (with Language-Code header - translated)',
    type: ApiResponseDto<TranslatedVehicleTypeExampleDto[]>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async getVehiclesByCityId(
    @Param('cityId') id: string,
    @Headers('language-code') languageCode?: string,
  ) {
    const data = await this.vehicleTypeService.findActiveVehicleTypesByCityId(
      id,
      languageCode,
    );
    return {
      success: true,
      message: 'Vehicles fetched successfully',
      data,
      timestamp: Date.now(),
    };
  }
  @Patch(':id')
  @ApiOperation({ summary: 'Update a city' })
  @ApiResponse({ status: 200, type: CityUpdateApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @UseGuards(CaslAbilityGuard)
  @Ability('city:edit')
  async update(@Param('id') id: string, @Body() body: UpdateCityDto) {
    const data = await this.cityService.updateCity(id, body as any);
    return {
      success: true,
      message: 'City updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':cityId/status')
  @ApiOperation({ summary: 'Change city status (active/inactive)' })
  @ApiResponse({ status: 200, type: CityStatusChangeApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @UseGuards(CaslAbilityGuard)
  @Ability('city:status_update')
  async changeStatus(
    @Param('cityId') id: string,
    @Body() body: ChangeCityStatusDto,
  ) {
    const data = await this.cityService.changeCityStatus(
      id,
      body.status as any,
    );
    return {
      success: true,
      message: 'City status changed successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a city (soft delete)' })
  @ApiResponse({ status: 200, type: CityDeleteApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @UseGuards(CaslAbilityGuard)
  @Ability('city:manage')
  async remove(@Param('id') id: string) {
    const data = await this.cityService.deleteCity(id);
    return {
      success: true,
      message: 'City deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}
