import { Module } from '@nestjs/common';
import { ProductController } from './product.controller';
import { ProductModule as SharedProductModule } from '@shared/shared/modules/product/product.module';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [SharedProductModule],
  providers: [CityAdminRepository],
  controllers: [ProductController],
})
export class ApiProductModule {}
