import { Injectable, Logger } from '@nestjs/common';
import {
  FareCalculationContext,
  CityProductFareMetrics,
  CityProductFareCalculationContext,
  FareCalculationResult,
  FareCalculationError,
  BatchFareCalculationResult,
  FareCalculationSummary,
  FareCalculationOptions,
} from './interfaces/fare-calculation.interface';
import { ChargeCalculationResult } from './interfaces/charge-calculation.interface';
import { TaxCalculationResult } from './interfaces/tax-calculation.interface';
import { CommissionCalculationResult } from './interfaces/commission-calculation.interface';
import { CityProductFareWithCharges } from '../interfaces/interface';
import { FareValidationService } from './validation/fare-validation.service';
import { ChargeProcessorService } from './charge-processor.service';

@Injectable()
export class FareCalculatorService {
  private readonly logger = new Logger(FareCalculatorService.name);

  constructor(
    private readonly chargeProcessor: ChargeProcessorService,
    private readonly validationService: FareValidationService,
  ) {}

  /**
   * Calculate fare for a single city product with its charges
   */
  async calculateFare(
    cityProductFareWithCharges: CityProductFareWithCharges,
    context: FareCalculationContext,
    metrics: CityProductFareMetrics,
    options?: FareCalculationOptions,
  ): Promise<FareCalculationResult | FareCalculationError> {
    const startTime = Date.now();

    try {
      // Step 0: Create combined context with metrics FIRST (before validation)
      const combinedContext: CityProductFareCalculationContext = {
        ...context,
        ...metrics,
      };

      // Step 1: Validate inputs (now with metrics included)
      const contextValidation =
        this.validationService.validateContext(combinedContext);
      if (!contextValidation.isValid) {
        this.logger.error(
          'Invalid fare calculation context',
          contextValidation.errors,
        );
        return {
          cityProductId: cityProductFareWithCharges.cityProductId,
          cityProductFareId: cityProductFareWithCharges.cityProductFareId,
          error: `Invalid context: ${contextValidation.errors.join(', ')}`,
          details: contextValidation,
          timestamp: new Date(),
        };
      }

      const cityProductValidation =
        this.validationService.validateCityProductFareWithCharges(
          cityProductFareWithCharges,
        );
      if (!cityProductValidation.isValid) {
        this.logger.error(
          'Invalid city product fare data',
          cityProductValidation.errors,
        );
        return {
          cityProductId: cityProductFareWithCharges.cityProductId,
          cityProductFareId: cityProductFareWithCharges.cityProductFareId,
          error: `Invalid city product fare: ${cityProductValidation.errors.join(', ')}`,
          details: cityProductValidation,
          timestamp: new Date(),
        };
      }

      this.logger.debug(
        `Starting fare calculation for city product ${cityProductFareWithCharges.cityProductId}`,
        {
          cityProductFareId: cityProductFareWithCharges.cityProductFareId,
          chargeGroupsCount: cityProductFareWithCharges.charges.length,
        },
      );

      // Step 2: Process all charges
      const chargeResults = await this.processCharges(
        cityProductFareWithCharges,
        combinedContext,
        options,
      );

      if (chargeResults.length === 0) {
        this.logger.warn(
          `No charges processed for city product ${cityProductFareWithCharges.cityProductId}`,
        );
      }

      // Step 3: Calculate subtotal from charges
      const subtotal = chargeResults.reduce(
        (sum, charge) => sum + charge.calculatedAmount,
        0,
      );

      // Step 4: Calculate taxes on charges
      const taxResults = await this.calculateTaxesOnCharges(chargeResults);

      // Step 5: Calculate commissions
      const commissionResults = await this.calculateCommissions(
        chargeResults,
        subtotal,
      );

      // Step 6: Calculate totals
      const totalTaxes = taxResults.reduce(
        (sum, tax) => sum + tax.totalTaxAmount,
        0,
      );

      const totalCommissions = commissionResults.reduce(
        (sum, commission) => sum + commission.totalCommissionWithTax,
        0,
      );

      // DEBUG: Log detailed breakdown
      this.logger.debug(`🔍 FARE CALCULATION BREAKDOWN for ${cityProductFareWithCharges.cityProductId}:`, {
        chargeBreakdown: chargeResults.map(charge => ({
          chargeId: charge.chargeId,
          chargeName: charge.chargeName,
          calculatedAmount: charge.calculatedAmount,
          appliedTaxes: charge.appliedTaxes?.map(tax => ({
            taxGroupName: tax.taxGroupName,
            baseAmount: tax.baseAmount,
            totalTaxAmount: tax.totalTaxAmount,
          })),
        })),
        subtotal,
        totalTaxes,
        totalCommissions,
      });

      // Step 7: Calculate final amounts (Uber-like model)
      const passengerFare = subtotal + totalTaxes; // Customer pays base fare + taxes only
      const driverEarnings = Math.max(0, subtotal - totalCommissions); // Driver receives base fare minus commission (minimum 0)
      const platformRevenue = totalCommissions; // Platform earns commission
      const grandTotal = passengerFare; // Legacy field for backward compatibility

      this.logger.debug(`🎯 FINAL FARE CALCULATION RESULT:`, {
        subtotal,
        totalTaxes,
        totalCommissions,
        passengerFare,
        driverEarnings,
        platformRevenue,
        grandTotal,
      });

      // Validation: Warn if commission exceeds subtotal (driver would earn nothing)
      if (totalCommissions > subtotal) {
        this.logger.warn(
          `Commission (${totalCommissions}) exceeds subtotal (${subtotal}) for city product ${cityProductFareWithCharges.cityProductId}. Driver earnings set to 0.`,
        );
      }

      // Step 8: Create calculation summary
      const calculationSummary = this.createCalculationSummary(
        chargeResults,
        taxResults,
        commissionResults,
      );

      const result: FareCalculationResult = {
        cityProductId: cityProductFareWithCharges.cityProductId,
        cityProductFareId: cityProductFareWithCharges.cityProductFareId,
        currency: context.currency || 'USD',
        chargeBreakdown: chargeResults,
        taxBreakdown: taxResults,
        commissionBreakdown: commissionResults,
        subtotal,
        totalTaxes,
        totalCommissions,
        passengerFare,
        driverEarnings,
        platformRevenue,
        grandTotal,
        calculatedAt: new Date(),
        calculationSummary,
      };

      const calculationTime = Date.now() - startTime;
      this.logger.debug(
        `Fare calculation completed for city product ${cityProductFareWithCharges.cityProductId}`,
        {
          passengerFare,
          driverEarnings,
          platformRevenue,
          subtotal,
          totalTaxes,
          totalCommissions,
          calculationTime: `${calculationTime}ms`,
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error calculating fare for city product ${cityProductFareWithCharges.cityProductId}`,
        error,
      );

      return {
        cityProductId: cityProductFareWithCharges.cityProductId,
        cityProductFareId: cityProductFareWithCharges.cityProductFareId,
        error: `Fare calculation failed: ${error instanceof Error ? error.message : String(error)}`,
        details: error,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Calculate fares for multiple city products
   * Note: Each city product should have the same metrics for batch processing
   */
  async calculateBatchFares(
    cityProductFaresWithCharges: CityProductFareWithCharges[],
    context: FareCalculationContext,
    metrics: CityProductFareMetrics,
    options?: FareCalculationOptions,
  ): Promise<BatchFareCalculationResult> {
    const startTime = Date.now();
    const results: FareCalculationResult[] = [];
    const errors: FareCalculationError[] = [];

    this.logger.debug(
      `Starting batch fare calculation for ${cityProductFaresWithCharges.length} city products`,
    );

    for (const cityProductFareWithCharges of cityProductFaresWithCharges) {
      try {
        const result = await this.calculateFare(
          cityProductFareWithCharges,
          context,
          metrics,
          options,
        );

        if ('error' in result) {
          errors.push(result);
        } else {
          results.push(result);
        }
      } catch (error) {
        this.logger.error(
          `Unexpected error in batch calculation for city product ${cityProductFareWithCharges.cityProductId}`,
          error,
        );

        errors.push({
          cityProductId: cityProductFareWithCharges.cityProductId,
          cityProductFareId: cityProductFareWithCharges.cityProductFareId,
          error: `Unexpected error: ${error instanceof Error ? error.message : String(error)}`,
          details: error,
          timestamp: new Date(),
        });
      }
    }

    const totalCalculationTime = Date.now() - startTime;

    this.logger.debug(`Batch fare calculation completed`, {
      totalProcessed: cityProductFaresWithCharges.length,
      successful: results.length,
      failed: errors.length,
      totalCalculationTime: `${totalCalculationTime}ms`,
    });

    return {
      status: errors.length === 0,
      results,
      errors,
      summary: {
        totalProcessed: cityProductFaresWithCharges.length,
        successful: results.length,
        failed: errors.length,
        totalCalculationTime,
      },
    };
  }

  /**
   * Process all charges for a city product fare
   */
  private async processCharges(
    cityProductFareWithCharges: CityProductFareWithCharges,
    context: CityProductFareCalculationContext,
    options?: FareCalculationOptions,
  ): Promise<ChargeCalculationResult[]> {
    const allChargeResults: ChargeCalculationResult[] = [];

    // Process charges from each charge group
    for (const chargeGroup of cityProductFareWithCharges.charges) {
      try {
        const chargeGroupResults =
          await this.chargeProcessor.processChargeGroup(
            chargeGroup,
            context,
            options,
          );
        allChargeResults.push(...chargeGroupResults);
      } catch (error) {
        this.logger.error(
          `Error processing charge group ${chargeGroup.chargeGroupId}`,
          error,
        );
        // Continue processing other charge groups
      }
    }

    return allChargeResults;
  }

  /**
   * Calculate taxes on all charges
   */
  private async calculateTaxesOnCharges(
    chargeResults: ChargeCalculationResult[],
  ): Promise<TaxCalculationResult[]> {
    const taxResults: TaxCalculationResult[] = [];

    for (const chargeResult of chargeResults) {
      if (chargeResult.appliedTaxes && chargeResult.appliedTaxes.length > 0) {
        taxResults.push(...chargeResult.appliedTaxes);
      }
    }

    return taxResults;
  }

  /**
   * Calculate commissions on charges
   */
  private async calculateCommissions(
    chargeResults: ChargeCalculationResult[],
    _subtotal: number, // Prefixed with underscore to indicate intentionally unused
  ): Promise<CommissionCalculationResult[]> {
    const commissionResults: CommissionCalculationResult[] = [];

    for (const chargeResult of chargeResults) {
      if (chargeResult.appliedCommission) {
        commissionResults.push(chargeResult.appliedCommission);
      }
    }

    return commissionResults;
  }

  /**
   * Create calculation summary
   */
  private createCalculationSummary(
    chargeResults: ChargeCalculationResult[],
    taxResults: TaxCalculationResult[],
    commissionResults: CommissionCalculationResult[],
  ): FareCalculationSummary {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Collect errors from charge calculations
    chargeResults.forEach((charge) => {
      if (!charge.success && charge.error) {
        errors.push(`Charge ${charge.chargeName}: ${charge.error}`);
      }
    });

    // Collect errors from tax calculations
    taxResults.forEach((tax) => {
      if (!tax.success && tax.error) {
        errors.push(`Tax ${tax.taxGroupName}: ${tax.error}`);
      }
    });

    // Collect errors from commission calculations
    commissionResults.forEach((commission) => {
      if (!commission.success && commission.error) {
        errors.push(
          `Commission ${commission.commissionName}: ${commission.error}`,
        );
      }
    });

    const summary: FareCalculationSummary = {
      totalCharges: chargeResults.length,
      totalChargeGroups: new Set(chargeResults.map((c) => c.chargeId)).size,
      totalTaxGroups: taxResults.length,
      totalCommissions: commissionResults.length,
      hasErrors: errors.length > 0,
    };

    if (errors.length > 0) {
      summary.errors = errors;
    }

    if (warnings.length > 0) {
      summary.warnings = warnings;
    }

    return summary;
  }
}
