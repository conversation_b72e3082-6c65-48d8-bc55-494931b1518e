import { Injectable, Logger } from '@nestjs/common';
import {
  ChargeCalculationContext,
  ChargeCalculationResult,
  ChargeCalculationBreakdown,
  ConditionEvaluationResult,
  MeterValueResult,
  PriceCalculationInput,
  PriceCalculationResult,
  TieredCalculationBreakdown,
} from './interfaces/charge-calculation.interface';
import {
  Charge,
  ChargeMeter,
  PriceModel,
  TierConfig,
} from '@shared/shared/repositories/models/charge.model';
import { ChargeGroupWithCharges } from '../interfaces/interface';
import { TaxCalculatorService } from './tax-calculator.service';
import { CommissionCalculatorService } from './commission-calculator.service';
import { ConditionMatcherService } from './utils';
import {
  CityProductFareCalculationContext,
  FareCalculationOptions,
} from './interfaces/fare-calculation.interface';

@Injectable()
export class ChargeProcessorService {
  private readonly logger = new Logger(ChargeProcessorService.name);

  constructor(
    private readonly taxCalculator: TaxCalculatorService,
    private readonly commissionCalculator: CommissionCalculatorService,
    private readonly conditionMatcher: ConditionMatcherService,
  ) { }

  /**
   * Process all charges in a charge group
   */
  async processChargeGroup(
    chargeGroup: ChargeGroupWithCharges,
    context: CityProductFareCalculationContext,
    options?: FareCalculationOptions,
  ): Promise<ChargeCalculationResult[]> {
    this.logger.debug(
      `Processing charge group ${chargeGroup.chargeGroupId} with ${chargeGroup.charges.length} charges`,
    );

    const results: ChargeCalculationResult[] = [];
    const referenceChargeAmounts = new Map<string, number>();

    // First pass: Calculate non-percentage charges
    for (const charge of chargeGroup.charges) {
      if (charge.priceModel !== PriceModel.PERCENTAGE_OF_CHARGE) {
        try {
          const result = await this.processCharge(
            charge,
            {
              ...context,
              referenceChargeAmounts,
            },
            options,
          );
          results.push(result);

          if (result.success) {
            referenceChargeAmounts.set(charge.id, result.calculatedAmount);
          }
        } catch (error) {
          this.logger.error(`Error processing charge ${charge.id}`, error);
          results.push(this.createErrorResult(charge, error));
        }
      }
    }

    // Second pass: Calculate percentage-based charges
    for (const charge of chargeGroup.charges) {
      if (charge.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) {
        try {
          const result = await this.processCharge(
            charge,
            {
              ...context,
              referenceChargeAmounts,
            },
            options,
          );
          results.push(result);
        } catch (error) {
          this.logger.error(
            `Error processing percentage charge ${charge.id}`,
            error,
          );
          results.push(this.createErrorResult(charge, error));
        }
      }
    }

    return results;
  }

  /**
   * Process a single charge
   */
  async processCharge(
    charge: Charge,
    context: ChargeCalculationContext,
    options?: FareCalculationOptions,
  ): Promise<ChargeCalculationResult> {
    const startTime = options?.enablePerformanceLogging ? Date.now() : 0;
    this.logger.debug(`Processing charge ${charge.id} (${charge.name})`);

    try {
      // Step 1: Evaluate condition
      const conditionResult = await this.evaluateCondition(charge, context);

      if (!conditionResult.conditionMet) {
        return this.createSkippedResult(charge, conditionResult);
      }

      // Step 2: Get meter value if needed
      const meterResult = this.getMeterValue(charge, context);

      // Step 3: Calculate price
      const priceResult = await this.calculatePrice(
        charge,
        context,
        meterResult?.value,
      );

      if (!priceResult.success) {
        return this.createErrorResult(
          charge,
          new Error(priceResult.error || 'Price calculation failed'),
        );
      }

      // Step 4: Apply taxes if charge has tax group (unless skipTaxBreakdown is true)
      this.logger.debug(`🔍 TAX CALCULATION DEBUG for charge ${charge.id}:`, {
        chargeId: charge.id,
        chargeName: charge.name,
        hasTaxGroup: !!charge.taxGroup,
        taxGroupId: charge.taxGroup?.id,
        taxGroupName: charge.taxGroup?.name,
        baseAmount: priceResult.amount,
        skipTaxBreakdown: options?.skipTaxBreakdown,
      });

      const appliedTaxes =
        charge.taxGroup && !options?.skipTaxBreakdown
          ? [
            await this.taxCalculator.calculateTax({
              taxGroup: charge.taxGroup,
              baseAmount: priceResult.amount,
            }),
          ]
          : [];

      if (appliedTaxes.length > 0) {
        this.logger.debug(`🔍 TAX CALCULATION RESULT for charge ${charge.id}:`, {
          taxResults: appliedTaxes.map(tax => ({
            taxGroupId: tax.taxGroupId,
            taxGroupName: tax.taxGroupName,
            baseAmount: tax.baseAmount,
            totalTaxAmount: tax.totalTaxAmount,
            totalPercentage: tax.totalPercentage,
            success: tax.success,
            error: tax.error,
          })),
        });
      }

      // Step 5: Apply commission if charge has commission (unless skipCommission is true)
      const appliedCommission =
        charge.commission && !options?.skipCommission
          ? await this.commissionCalculator.calculateCommission({
            commission: charge.commission,
            baseAmount: priceResult.amount,
          })
          : undefined;

      // Performance logging
      if (options?.enablePerformanceLogging && startTime > 0) {
        const duration = Date.now() - startTime;
        this.logger.debug(
          `Charge ${charge.id} processed in ${duration}ms (skipCommission: ${options.skipCommission}, skipTaxBreakdown: ${options.skipTaxBreakdown})`,
        );
      }

      // Step 6: Create result
      const result: ChargeCalculationResult = {
        chargeId: charge.id,
        chargeName: charge.name,
        chargeType: charge.chargeType,
        priceModel: charge.priceModel,
        baseAmount: priceResult.amount,
        calculatedAmount: priceResult.amount,
        meter: charge.meter || undefined,
        meterValue: meterResult?.value,
        conditionMet: conditionResult.conditionMet,
        conditionDetails: conditionResult.evaluationDetails,
        calculationBreakdown: priceResult.breakdown,
        appliedTaxes: appliedTaxes.filter((tax) => tax.success),
        appliedCommission,
        success: true,
      };

      this.logger.debug(
        `Charge ${charge.id} processed successfully: ${result.calculatedAmount}`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Error processing charge ${charge.id}`, error);
      return this.createErrorResult(charge, error);
    }
  }

  /**
   * Evaluate charge condition
   */
  private async evaluateCondition(
    charge: Charge,
    context: ChargeCalculationContext,
  ): Promise<ConditionEvaluationResult> {
    if (!charge.condition) {
      return { conditionMet: true };
    }

    try {
      return await this.conditionMatcher.evaluateCondition(
        charge.condition,
        context,
      );
    } catch (error) {
      this.logger.error(
        `Error evaluating condition for charge ${charge.id}`,
        error,
      );
      return {
        conditionMet: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Get meter value for charge
   */
  private getMeterValue(
    charge: Charge,
    context: ChargeCalculationContext,
  ): MeterValueResult | null {
    if (!charge.meter) {
      return null;
    }

    let value: number;
    let unit: string;

    switch (charge.meter) {
      case ChargeMeter.PICKUP_DISTANCE:
        value = context.pickupDistance;
        unit = 'km';
        break;
      case ChargeMeter.PICKUP_DURATION:
        value = context.pickupDuration;
        unit = 'minutes';
        break;
      case ChargeMeter.PICKUP_WAIT_DURATION:
        value = context.pickupWaitDuration;
        unit = 'minutes';
        break;
      case ChargeMeter.TRIP_DURATION:
        value = context.tripDuration;
        unit = 'minutes';
        break;
      case ChargeMeter.TRIP_WAIT_DURATION:
        value = context.tripWaitDuration;
        unit = 'minutes';
        break;
      case ChargeMeter.TRIP_DISTANCE:
        value = context.tripDistance;
        unit = 'km';
        break;
      default:
        this.logger.warn(`Unknown meter type: ${charge.meter}`);
        return null;
    }

    return {
      meter: charge.meter,
      value,
      unit,
    };
  }

  /**
   * Calculate price based on price model
   */
  private async calculatePrice(
    charge: Charge,
    context: ChargeCalculationContext,
    meterValue?: number,
  ): Promise<PriceCalculationResult> {
    const input: PriceCalculationInput = {
      priceModel: charge.priceModel,
      priceConfig: charge.price,
      meterValue,
      referenceAmount: charge.percentageOfChargeId
        ? context.referenceChargeAmounts?.get(charge.percentageOfChargeId)
        : undefined,
      context,
    };

    switch (charge.priceModel) {
      case PriceModel.FLAT_AMOUNT:
        return this.calculateFlatAmount(input);
      case PriceModel.LINEAR_RATE:
        return this.calculateLinearRate(input);
      case PriceModel.TIERED:
        return this.calculateTiered(input);
      case PriceModel.PERCENTAGE_OF_CHARGE:
        return this.calculatePercentageOfCharge(input);
      case PriceModel.FORMULA:
        return this.calculateFormula(input);
      default:
        return {
          amount: 0,
          breakdown: this.createEmptyBreakdown(charge.priceModel),
          success: false,
          error: `Unsupported price model: ${charge.priceModel}`,
        };
    }
  }

  /**
   * Calculate flat amount
   */
  private calculateFlatAmount(
    input: PriceCalculationInput,
  ): PriceCalculationResult {
    const amount = input.priceConfig?.amount || 0;

    return {
      amount,
      breakdown: {
        priceModel: input.priceModel,
        flatAmount: amount,
        finalAmount: amount,
      },
      success: true,
    };
  }

  /**
   * Calculate linear rate
   */
  private calculateLinearRate(
    input: PriceCalculationInput,
  ): PriceCalculationResult {
    if (input.meterValue === undefined) {
      return {
        amount: 0,
        breakdown: this.createEmptyBreakdown(input.priceModel),
        success: false,
        error: 'Meter value required for linear rate calculation',
      };
    }

    const rate = input.priceConfig?.rate || 0;
    const amount = rate * input.meterValue;

    return {
      amount,
      breakdown: {
        priceModel: input.priceModel,
        rate,
        units: input.meterValue,
        linearAmount: amount,
        finalAmount: amount,
      },
      success: true,
    };
  }

  /**
   * Calculate tiered pricing
   */
  private calculateTiered(
    input: PriceCalculationInput,
  ): PriceCalculationResult {
    if (input.meterValue === undefined) {
      return {
        amount: 0,
        breakdown: this.createEmptyBreakdown(input.priceModel),
        success: false,
        error: 'Meter value required for tiered calculation',
      };
    }

    const tiers: TierConfig[] = input.priceConfig?.tiers || [];
    if (tiers.length === 0) {
      return {
        amount: 0,
        breakdown: this.createEmptyBreakdown(input.priceModel),
        success: false,
        error: 'No tiers configured for tiered pricing',
      };
    }

    // DEBUG: Log tier configuration and meter value
    this.logger.debug(`🔍 TIERED CALCULATION DEBUG:`, {
      meterValue: input.meterValue,
      tiersCount: tiers.length,
      tiers: tiers.map((t, i) => ({
        index: i,
        from: t.From,
        to: t.To,
        flatFee: t.Flat_fee,
        rate: t.Rate,
      })),
    });

    let totalAmount = 0;
    let remainingUnits = input.meterValue;
    const tieredBreakdown: TieredCalculationBreakdown[] = [];

    for (let i = 0; i < tiers.length && remainingUnits > 0; i++) {
      const tier = tiers[i];
      const tierMax = tier.To === 'inf' ? Infinity : tier.To;
      const unitsInTier = Math.min(remainingUnits, tierMax - tier.From);

      this.logger.debug(`🔍 Processing Tier ${i}:`, {
        tierFrom: tier.From,
        tierTo: tier.To,
        tierMax,
        remainingUnits,
        unitsInTier,
        flatFee: tier.Flat_fee,
        rate: tier.Rate,
      });

      if (unitsInTier <= 0) {
        this.logger.debug(`⚠️ Skipping Tier ${i}: unitsInTier <= 0`);
        continue;
      }

      let tierAmount = tier.Flat_fee || 0;
      this.logger.debug(`💰 Tier ${i} - Starting with flat fee: ${tierAmount}`);

      if (tier.Rate) {
        const rateAmount = tier.Rate * unitsInTier;
        tierAmount += rateAmount;
        this.logger.debug(`💰 Tier ${i} - Adding rate: ${tier.Rate} × ${unitsInTier} = ${rateAmount}, new tierAmount: ${tierAmount}`);
      }

      totalAmount += tierAmount;
      remainingUnits -= unitsInTier;

      this.logger.debug(`💰 Tier ${i} - Final:`, {
        tierAmount,
        totalAmountSoFar: totalAmount,
        remainingUnits,
      });

      tieredBreakdown.push({
        tierIndex: i,
        from: tier.From,
        to: tier.To,
        unitsInTier,
        flatFee: tier.Flat_fee,
        rate: tier.Rate,
        tierAmount,
      });
    }

    this.logger.debug(`🎯 TIERED CALCULATION FINAL RESULT:`, {
      totalAmount,
      breakdown: tieredBreakdown,
    });

    return {
      amount: totalAmount,
      breakdown: {
        priceModel: input.priceModel,
        tieredBreakdown,
        totalTieredAmount: totalAmount,
        finalAmount: totalAmount,
      },
      success: true,
    };
  }

  /**
   * Calculate percentage of charge
   */
  private calculatePercentageOfCharge(
    input: PriceCalculationInput,
  ): PriceCalculationResult {
    if (input.referenceAmount === undefined) {
      return {
        amount: 0,
        breakdown: this.createEmptyBreakdown(input.priceModel),
        success: false,
        error: 'Reference charge amount not found for percentage calculation',
      };
    }

    const percentage = input.priceConfig?.percentage || 0;
    const amount = (input.referenceAmount * percentage) / 100;

    return {
      amount,
      breakdown: {
        priceModel: input.priceModel,
        percentage,
        referenceAmount: input.referenceAmount,
        percentageAmount: amount,
        finalAmount: amount,
      },
      success: true,
    };
  }

  /**
   * Calculate formula-based pricing
   */
  private calculateFormula(
    input: PriceCalculationInput,
  ): PriceCalculationResult {
    // This is a simplified implementation
    // In a real system, you'd want a proper formula parser/evaluator
    const formula = input.priceConfig?.formula;

    if (!formula) {
      return {
        amount: 0,
        breakdown: this.createEmptyBreakdown(input.priceModel),
        success: false,
        error: 'No formula provided for formula-based pricing',
      };
    }

    try {
      // Simple variable substitution (extend as needed)
      const variables = {
        pickupDistance: input.context?.pickupDistance || 0,
        pickupDuration: input.context?.pickupDuration || 0,
        tripDistance: input.context?.tripDistance || 0,
        tripDuration: input.context?.tripDuration || 0,
        meterValue: input.meterValue || 0,
      };

      // WARNING: This is a simplified implementation
      // In production, use a proper formula parser for security
      let evaluatedFormula = formula;
      Object.entries(variables).forEach(([key, value]) => {
        evaluatedFormula = evaluatedFormula.replace(
          new RegExp(`\\b${key}\\b`, 'g'),
          value.toString(),
        );
      });

      // Basic math evaluation (extend with proper parser)
      const amount = eval(evaluatedFormula);

      return {
        amount,
        breakdown: {
          priceModel: input.priceModel,
          formula,
          formulaVariables: variables,
          formulaResult: amount,
          finalAmount: amount,
        },
        success: true,
      };
    } catch (error) {
      return {
        amount: 0,
        breakdown: this.createEmptyBreakdown(input.priceModel),
        success: false,
        error: `Formula evaluation failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Create empty breakdown
   */
  private createEmptyBreakdown(
    priceModel: PriceModel,
  ): ChargeCalculationBreakdown {
    return {
      priceModel,
      finalAmount: 0,
    };
  }

  /**
   * Create error result
   */
  private createErrorResult(
    charge: Charge,
    error: any,
  ): ChargeCalculationResult {
    return {
      chargeId: charge.id,
      chargeName: charge.name,
      chargeType: charge.chargeType,
      priceModel: charge.priceModel,
      baseAmount: 0,
      calculatedAmount: 0,
      conditionMet: false,
      calculationBreakdown: this.createEmptyBreakdown(charge.priceModel),
      appliedTaxes: [],
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }

  /**
   * Create skipped result for failed condition
   */
  private createSkippedResult(
    charge: Charge,
    conditionResult: ConditionEvaluationResult,
  ): ChargeCalculationResult {
    return {
      chargeId: charge.id,
      chargeName: charge.name,
      chargeType: charge.chargeType,
      priceModel: charge.priceModel,
      baseAmount: 0,
      calculatedAmount: 0,
      conditionMet: false,
      conditionDetails: conditionResult.evaluationDetails,
      calculationBreakdown: this.createEmptyBreakdown(charge.priceModel),
      appliedTaxes: [],
      success: true, // Condition not met is not an error
    };
  }
}
