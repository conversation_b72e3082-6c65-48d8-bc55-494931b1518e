'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateCommission, useUpdateCommission } from '../api/mutations';
import { useGetCommission } from '../api/queries';
import { useGetActiveTaxGroups } from '@/modules/tax-group/api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { CommissionType } from '../types/commissions';

// ============================================
// ZOD VALIDATION SCHEMA
// ============================================
const commissionSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Name is required')
      .min(2, 'Name must be at least 2 characters')
      .max(100, 'Name must not exceed 100 characters'),
    description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
    type: z.enum(['percentage', 'flat'], {
      message: 'Commission type is required',
    }),
    percentageValue: z.number().min(0.01).max(100).optional(),
    flatValue: z.number().min(0.01).optional(),
    taxGroupId: z.string().optional(),
  })
  .refine(
    data => {
      if (data.type === 'percentage') {
        return data.percentageValue !== undefined && data.percentageValue > 0;
      }
      return true;
    },
    {
      message: 'Percentage value is required when type is percentage',
      path: ['percentageValue'],
    }
  )
  .refine(
    data => {
      if (data.type === 'flat') {
        return data.flatValue !== undefined && data.flatValue > 0;
      }
      return true;
    },
    {
      message: 'Flat value is required when type is flat',
      path: ['flatValue'],
    }
  );

type CommissionFormValues = z.infer<typeof commissionSchema>;

// ============================================
// COMPONENT PROPS
// ============================================
interface CommissionsModalProps {
  itemId?: string | null;
  isOpen?: boolean;
  onClose?: () => void;
  mode?: 'create' | 'edit';
}

// ============================================
// MODAL COMPONENT
// ============================================
export const CommissionsModal = ({
  itemId,
  isOpen,
  onClose,
  mode = 'create',
}: CommissionsModalProps) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const createMutation = useCreateCommission();
  const updateMutation = useUpdateCommission();
  const itemQuery = useGetCommission(itemId || null);
  const taxGroupsQuery = useGetActiveTaxGroups();
  const queryClient = useQueryClient();
  const { withPermission } = useRoleBasedAccess();

  // Use external open state if provided, otherwise use internal state
  const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
  const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

  const form = useForm<CommissionFormValues>({
    resolver: zodResolver(commissionSchema),
    defaultValues: {
      name: '',
      description: '',
      type: 'percentage',
      percentageValue: undefined,
      flatValue: undefined,
      taxGroupId: 'none',
    },
  });

  const {
    formState: { errors },
    reset,
    control,
    handleSubmit,
    watch,
  } = form;

  const selectedType = watch('type');
  const isDataLoading = itemQuery.isLoading;

  // Reset form when itemId changes or modal opens
  useEffect(() => {
    if (mode === 'edit' && itemQuery.data?.data && modalOpen && !isDataLoading) {
      const item = itemQuery.data.data;
      reset({
        name: item.name,
        description: item.description || '',
        type: item.type,
        percentageValue: item.percentageValue
          ? typeof item.percentageValue === 'string'
            ? parseFloat(item.percentageValue)
            : item.percentageValue
          : undefined,
        flatValue: item.flatValue
          ? typeof item.flatValue === 'string'
            ? parseFloat(item.flatValue)
            : item.flatValue
          : undefined,
        taxGroupId: item.taxGroupId || 'none',
      });
    } else if (mode === 'create' && modalOpen) {
      reset({
        name: '',
        description: '',
        type: 'percentage',
        percentageValue: undefined,
        flatValue: undefined,
        taxGroupId: 'none',
      });
    }
  }, [itemQuery.data, reset, mode, modalOpen, isDataLoading, isOpen]);

  const onSubmit = async (data: CommissionFormValues) => {
    try {
      const payload = {
        name: data.name,
        description: data.description || undefined,
        type: data.type as CommissionType,
        percentageValue: data.type === 'percentage' ? data.percentageValue : null,
        flatValue: data.type === 'flat' ? data.flatValue : null,
        taxGroupId: data.taxGroupId && data.taxGroupId !== 'none' ? data.taxGroupId : undefined,
      };

      if (mode === 'create') {
        createMutation.mutate(payload, {
          onSuccess: () => {
            toast.success('Commission created successfully');
            handleClose();
            queryClient.invalidateQueries({ queryKey: ['commissions'] });
          },
        });
      } else if (mode === 'edit' && itemId) {
        updateMutation.mutate(
          { id: itemId, ...payload },
          {
            onSuccess: () => {
              toast.success('Commission updated successfully');
              handleClose();
              queryClient.invalidateQueries({ queryKey: ['commissions'] });
              queryClient.invalidateQueries({ queryKey: ['commission', itemId] });
            },
          }
        );
      }
    } catch (error: any) {
      console.error('Submit error:', error);
    }
  };

  const handleClose = () => {
    setModalOpen(false);
    reset();
  };

  const isLoading = mode === 'create' ? createMutation.isPending : updateMutation.isPending;

  // Show loading state for edit mode
  if (mode === 'edit' && itemQuery.isLoading) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Loading...</DialogTitle>
            <DialogDescription>Please wait while we load the data.</DialogDescription>
          </DialogHeader>
          <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state for edit mode
  if (mode === 'edit' && itemQuery.error) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>Failed to load data.</DialogDescription>
          </DialogHeader>
          <div className='text-center py-8'>
            <p className='text-red-600'>Failed to load data</p>
            <Button onClick={handleClose} className='mt-4'>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const content = (
    <DialogContent
      onInteractOutside={e => {
        e.preventDefault();
      }}
      className='max-w-2xl max-h-[90vh] overflow-y-auto'
    >
      <DialogHeader>
        <DialogTitle>{mode === 'create' ? 'Create New Commission' : 'Edit Commission'}</DialogTitle>
        <DialogDescription>
          {mode === 'create'
            ? 'Add a new commission to the system'
            : 'Update the commission information'}
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit(onSubmit)} className='space-y-4 py-4'>
        {/* Name Field */}
        <div className='flex flex-col gap-2'>
          <Label htmlFor='name'>Name *</Label>
          <Controller
            control={control}
            name='name'
            render={({ field }) => (
              <Input id='name' placeholder='Enter commission name' {...field} className='w-full' />
            )}
          />
          {errors.name && <ErrorMessage error={errors.name} />}
        </div>

        {/* Description Field */}
        <div className='flex flex-col gap-2'>
          <Label htmlFor='description'>Description</Label>
          <Controller
            control={control}
            name='description'
            render={({ field }) => (
              <Textarea
                id='description'
                placeholder='Enter description'
                {...field}
                className='w-full min-h-[80px] resize-none'
              />
            )}
          />
          {errors.description && <ErrorMessage error={errors.description} />}
        </div>

        {/* Type Field */}
        <div className='flex flex-col gap-2'>
          <Label htmlFor='type'>Commission Type *</Label>
          <Controller
            control={control}
            name='type'
            render={({ field }) => (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                key={`type-${field.value}-${modalOpen}`}
              >
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Select commission type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='percentage'>Percentage</SelectItem>
                  <SelectItem value='flat'>Flat Amount</SelectItem>
                </SelectContent>
              </Select>
            )}
          />
          {errors.type && <ErrorMessage error={errors.type} />}
        </div>

        {/* Conditional Value Fields */}
        {selectedType === 'percentage' && (
          <div className='flex flex-col gap-2'>
            <Label htmlFor='percentageValue'>Percentage Value (%) *</Label>
            <Controller
              control={control}
              name='percentageValue'
              render={({ field }) => (
                <Input
                  {...field}
                  id='percentageValue'
                  type='number'
                  step='0.01'
                  min='0.01'
                  max='100'
                  placeholder='Enter percentage (0.01 - 100)'
                  onChange={e => field.onChange(parseFloat(e.target.value) || undefined)}
                  value={field.value || ''}
                />
              )}
            />
            {errors.percentageValue && <ErrorMessage error={errors.percentageValue} />}
          </div>
        )}

        {selectedType === 'flat' && (
          <div className='flex flex-col gap-2'>
            <Label htmlFor='flatValue'>Flat Amount ($) *</Label>
            <Controller
              control={control}
              name='flatValue'
              render={({ field }) => (
                <Input
                  {...field}
                  id='flatValue'
                  type='number'
                  step='0.01'
                  min='0.01'
                  placeholder='Enter flat amount'
                  onChange={e => field.onChange(parseFloat(e.target.value) || undefined)}
                  value={field.value || ''}
                />
              )}
            />
            {errors.flatValue && <ErrorMessage error={errors.flatValue} />}
          </div>
        )}

        {/* Tax Group Field */}
        <div className='flex flex-col gap-2'>
          <Label htmlFor='taxGroupId'>Tax Group (Optional)</Label>
          <Controller
            control={control}
            name='taxGroupId'
            render={({ field }) => (
              <Select
                onValueChange={field.onChange}
                value={field.value}
                key={`taxGroup-${field.value}-${modalOpen}`}
              >
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Select tax group' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='none'>No tax group</SelectItem>
                  {taxGroupsQuery.data?.data?.map((taxGroup: any) => (
                    <SelectItem key={taxGroup.id} value={taxGroup.id}>
                      {taxGroup.name} ({taxGroup.totalPercentage}%)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.taxGroupId && <ErrorMessage error={errors.taxGroupId} />}
        </div>

        {/* Action Buttons */}
        <div className='flex gap-3 pt-4'>
          <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
            Cancel
          </Button>
          <Button type='submit' disabled={isLoading} className='flex-1'>
            {isLoading ? (
              <>
                {mode === 'create' ? 'Creating...' : 'Updating...'}
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : mode === 'create' ? (
              'Create Commission'
            ) : (
              'Update Commission'
            )}
          </Button>
        </div>
      </form>
    </DialogContent>
  );

  // For create mode, return button and dialog separately
  if (mode === 'create' && isOpen === undefined) {
    return (
      <>
        <Button
          className='cursor-pointer'
          variant='outline'
          onClick={() =>
            withPermission(RBAC_PERMISSIONS.COMMISSION.CREATE, () => setModalOpen(true))
          }
        >
          <Plus />
          Add Commission
        </Button>
        <Dialog open={modalOpen} onOpenChange={setModalOpen}>
          {content}
        </Dialog>
      </>
    );
  }

  // For edit mode or controlled create mode
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      {content}
    </Dialog>
  );
};
