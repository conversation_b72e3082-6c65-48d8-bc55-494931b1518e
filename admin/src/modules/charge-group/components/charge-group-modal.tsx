'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateChargeGroup, useUpdateChargeGroup } from '../api/mutations';
import { useGetChargeGroup } from '../api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

const chargeGroupSchema = z.object({
   name: z
      .string()
      .min(1, 'Charge group name is required')
      .min(2, 'Charge group name must be at least 2 characters')
      .max(35, 'Charge group name must not exceed 35 characters')
      .regex(/^[A-Za-z\s]+$/, 'Charge group name must contain only letters and spaces'),
   description: z.string().max(1000, 'Description must not exceed 1000 characters').optional(),
   identifier: z.string().max(255, 'Identifier must not exceed 255 characters').optional(),
});

type ChargeGroupFormValues = z.infer<typeof chargeGroupSchema>;

interface ChargeGroupModalProps {
  chargeGroupId?: string | null;
  isOpen?: boolean;
  onClose?: () => void;
  mode?: 'create' | 'edit';
}

export const ChargeGroupModal = ({
  chargeGroupId,
  isOpen,
  onClose,
  mode = 'create',
}: ChargeGroupModalProps) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const createChargeGroupMutation = useCreateChargeGroup();
  const updateChargeGroupMutation = useUpdateChargeGroup();
  const chargeGroupQuery = useGetChargeGroup(chargeGroupId || null);
  const queryClient = useQueryClient();
  const { withPermission } = useRoleBasedAccess();

  // Use external open state if provided, otherwise use internal state
  const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
  const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

  const form = useForm<ChargeGroupFormValues>({
    resolver: zodResolver(chargeGroupSchema),
    defaultValues: {
      name: '',
      description: '',
      identifier: '',
    },
  });

  const {
    formState: { errors },
    reset,
    control,
  } = form;

  const isDataLoading = chargeGroupQuery.isLoading;

  // Reset form when chargeGroupId changes or modal opens
  useEffect(() => {
    if (mode === 'edit' && chargeGroupQuery.data?.data && modalOpen && !isDataLoading) {
      const chargeGroup = chargeGroupQuery.data.data;
      reset({
        name: chargeGroup.name,
        description: chargeGroup.description || '',
        identifier: chargeGroup.identifier || '',
      });
    } else if (mode === 'create') {
      reset({
        name: '',
        description: '',
        identifier: '',
      });
    }
  }, [chargeGroupQuery.data, reset, mode, modalOpen, isDataLoading]);

  const onSubmit = async (data: ChargeGroupFormValues) => {
    try {
      // Auto-generate identifier from name: replace spaces with underscores and lowercase
      const identifier = data.name.toLowerCase().replace(/\s+/g, '_');

      const payload = {
        name: data.name,
        description: data.description || undefined,
        identifier,
      };

      if (mode === 'create') {
        createChargeGroupMutation.mutate(payload, {
          onSuccess: () => {
            toast.success('Charge group created successfully');
            handleClose();
            queryClient.invalidateQueries({ queryKey: ['charge-groups'] });
          },
        });
      } else if (mode === 'edit' && chargeGroupId) {
        updateChargeGroupMutation.mutate(
          { id: chargeGroupId, ...payload },
          {
            onSuccess: () => {
              toast.success('Charge group updated successfully');
              handleClose();
              queryClient.invalidateQueries({ queryKey: ['charge-groups'] });
              queryClient.invalidateQueries({
                queryKey: ['charge-group', chargeGroupId],
              });
            },
          }
        );
      }
    } catch (error: any) {
      console.error('Submit error:', error);
    }
  };

  const handleClose = () => {
    setModalOpen(false);
    reset();
  };

  const isLoading =
    mode === 'create' ? createChargeGroupMutation.isPending : updateChargeGroupMutation.isPending;

  // Show loading state for edit mode
  if (mode === 'edit' && chargeGroupQuery.isLoading) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Loading...</DialogTitle>
            <DialogDescription>
              Please wait while we load the charge group data.
            </DialogDescription>
          </DialogHeader>
          <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state for edit mode
  if (mode === 'edit' && chargeGroupQuery.error) {
    return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>Failed to load charge group data.</DialogDescription>
          </DialogHeader>
          <div className='text-center py-8'>
            <p className='text-red-600'>Failed to load charge group data</p>
            <Button onClick={handleClose} className='mt-4'>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const content = (
    <DialogContent
      onInteractOutside={e => {
        e.preventDefault();
      }}
      className='max-w-md max-h-[90vh] overflow-y-auto'
    >
      <DialogHeader>
        <DialogTitle>
          {mode === 'create' ? 'Create New Charge Group' : 'Edit Charge Group'}
        </DialogTitle>
        <DialogDescription>
          {mode === 'create'
            ? 'Add a new charge group to the system'
            : 'Update the charge group information'}
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
        <div className='flex flex-col gap-2'>
          <Label htmlFor='name'>Charge Group Name *</Label>
          <Controller
            control={control}
            name='name'
            render={({ field }) => (
              <Input
                id='name'
                placeholder='e.g. Premium Charges, Base Charges'
                {...field}
                className='w-full'
              />
            )}
          />
          {errors.name && <ErrorMessage error={errors.name} />}
        </div>

        <div className='flex flex-col gap-2'>
          <Label htmlFor='description'>Description</Label>
          <Controller
            control={control}
            name='description'
            render={({ field }) => (
              <Textarea
                id='description'
                placeholder='Enter charge group description'
                {...field}
                className='w-full min-h-[80px] resize-none'
              />
            )}
          />
          {errors.description && <ErrorMessage error={errors.description} />}
        </div>


        <div className='flex gap-3 pt-4'>
          <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
            Cancel
          </Button>
          <Button type='submit' disabled={isLoading} className='flex-1'>
            {isLoading ? (
              <>
                {mode === 'create' ? 'Creating...' : 'Updating...'}
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : mode === 'create' ? (
              'Create Charge Group'
            ) : (
              'Update Charge Group'
            )}
          </Button>
        </div>
      </form>
    </DialogContent>
  );

  // For create mode, return button and dialog separately
  if (mode === 'create' && isOpen === undefined) {
    return (
      <>
        <Button
          className='cursor-pointer'
          variant='outline'
          onClick={() =>
            withPermission(RBAC_PERMISSIONS.CHARGE_GROUPS.CREATE, () => setModalOpen(true))
          }
        >
          <Plus />
          Add Charge Group
        </Button>
        <Dialog open={modalOpen} onOpenChange={setModalOpen}>
          {content}
        </Dialog>
      </>
    );
  }

  // For edit mode or controlled create mode
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      {content}
    </Dialog>
  );
};