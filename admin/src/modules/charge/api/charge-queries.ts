import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import {
   ChargeResponse,
   ListChargesResponse,
   ChargeGroupChargeResponse,
   Charge,
} from '../types/charge';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

/**
 * Hook for listing all charges in a charge group (with priority data)
 */
export const useListCharges = (chargeGroupId: string | null) => {
   return useQuery({
      queryKey: ['charges', chargeGroupId],
      queryFn: async (): Promise<ListChargesResponse> => {
         const response: {
            success: boolean;
            message: string;
            data: ChargeGroupChargeResponse[];
            timestamp: number;
         } = await apiClient.get(`/charge-group-charges/charge-group/${chargeGroupId}`);

         // Transform ChargeGroupChargeResponse[] to Charge[] with priority data
         const charges: Charge[] = response.data.map((cgc: ChargeGroupChargeResponse) => ({
            ...cgc.charge,
            priority: cgc.priority,
            chargeGroupChargeId: cgc.id,
         }));

         return {
            success: response.success,
            message: response.message,
            data: charges,
            timestamp: response.timestamp,
         };
      },
      enabled: !!chargeGroupId,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for getting a single charge by ID
 */
export const useGetCharge = (chargeGroupId: string | null, chargeId: string | null) => {
   return useQuery({
      queryKey: ['charge', chargeGroupId, chargeId],
      queryFn: (): Promise<ChargeResponse> => {
         return apiClient.get(`/charge-groups/${chargeGroupId}/charges/${chargeId}`);
      },
      enabled: !!chargeGroupId && !!chargeId,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for getting a single charge by ID within charge group (new endpoint)
 */
export const useGetChargeByGroupAndId = (
   chargeGroupId: string | null,
   chargeId: string | null | undefined,
   enabled: boolean = true
) => {
   return useQuery({
      queryKey: ['charge-details', chargeGroupId, chargeId],
      queryFn: (): Promise<ChargeResponse> => {
         return apiClient.get(`/charges/charge-group/${chargeGroupId}/${chargeId}`);
      },
      enabled: !!chargeGroupId && !!chargeId && enabled,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for listing all charges across all charge groups (paginated with search)
 */
export const useListAllCharges = (
   page: number = 1,
   limit: number = 10,
   search?: string,
   isCommon?: boolean
) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      enabled: hasPermission(RBAC_PERMISSIONS.CHARGE.LIST),
      queryKey: ['all-charges', page, limit, search],
      queryFn: async () => {
         const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
         });

         if (search) {
            params.append('search', search);
         }

         if (isCommon) {
            params.append('isCommon', 'true');
         }

         const response = await apiClient.get(`/charges?${params.toString()}`);
         return response;
      },
      refetchOnWindowFocus: false,
   });
};
